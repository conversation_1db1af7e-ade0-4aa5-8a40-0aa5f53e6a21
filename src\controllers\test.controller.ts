import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';

export class TestController {
  constructor() {}

  /**
   * Test GET method
   */
  @get('/test')
  @response(200, {
    description: 'Test GET method',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            method: {type: 'string'},
            timestamp: {type: 'string'},
          },
        },
      },
    },
  })
  async testGet(): Promise<object> {
    return {
      status: true,
      message: 'GET method is working successfully',
      method: 'GET',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Test POST method
   */
  @post('/test')
  @response(200, {
    description: 'Test POST method',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            method: {type: 'string'},
            receivedData: {type: 'object'},
            timestamp: {type: 'string'},
          },
        },
      },
    },
  })
  async testPost(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              testData: {type: 'string'},
              value: {type: 'number'},
            },
          },
        },
      },
    })
    testData: any,
  ): Promise<object> {
    return {
      status: true,
      message: 'POST method is working successfully',
      method: 'POST',
      receivedData: testData,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Test PATCH method
   */
  @patch('/test/{id}')
  @response(200, {
    description: 'Test PATCH method',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            method: {type: 'string'},
            id: {type: 'string'},
            receivedData: {type: 'object'},
            timestamp: {type: 'string'},
          },
        },
      },
    },
  })
  async testPatch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              updateData: {type: 'string'},
              value: {type: 'number'},
            },
          },
        },
      },
    })
    updateData: any,
  ): Promise<object> {
    return {
      status: true,
      message: 'PATCH method is working successfully',
      method: 'PATCH',
      id: id,
      receivedData: updateData,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Test DELETE method
   */
  @del('/test/{id}')
  @response(200, {
    description: 'Test DELETE method',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            method: {type: 'string'},
            id: {type: 'string'},
            timestamp: {type: 'string'},
          },
        },
      },
    },
  })
  async testDelete(@param.path.string('id') id: string): Promise<object> {
    return {
      status: true,
      message: 'DELETE method is working successfully',
      method: 'DELETE',
      id: id,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Test OPTIONS method (handled automatically by LoopBack)
   * This endpoint provides information about available methods
   */
  @get('/test/options')
  @response(200, {
    description: 'Test OPTIONS method information',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            availableMethods: {type: 'array'},
            timestamp: {type: 'string'},
          },
        },
      },
    },
  })
  async testOptions(): Promise<object> {
    return {
      status: true,
      message: 'OPTIONS method is working successfully',
      availableMethods: ['GET', 'POST', 'PATCH', 'DELETE', 'OPTIONS'],
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Test all methods status
   */
  @get('/test/status')
  @response(200, {
    description: 'Test all methods status',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            methods: {type: 'object'},
            timestamp: {type: 'string'},
          },
        },
      },
    },
  })
  async testAllMethods(): Promise<object> {
    return {
      status: true,
      message: 'All HTTP methods are available for testing',
      methods: {
        GET: '/test - Test GET method',
        POST: '/test - Test POST method',
        PATCH: '/test/{id} - Test PATCH method',
        DELETE: '/test/{id} - Test DELETE method',
        OPTIONS: 'Automatically handled by LoopBack for all endpoints',
      },
      endpoints: {
        'GET /test': 'Basic GET test',
        'POST /test': 'POST test with request body',
        'PATCH /test/{id}': 'PATCH test with ID and request body',
        'DELETE /test/{id}': 'DELETE test with ID',
        'GET /test/options': 'OPTIONS method information',
        'GET /test/status': 'All methods status (this endpoint)',
      },
      timestamp: new Date().toISOString(),
    };
  }
}
