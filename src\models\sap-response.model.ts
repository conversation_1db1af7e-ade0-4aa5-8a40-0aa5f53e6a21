import {Entity, model, property} from '@loopback/repository';

@model()
export class SapResponse extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;


  @property({
    type: 'string',
  })
  Location?: string;
  @property({
    type: 'string',
  })
  Title?: string;

  @property({
    type: 'number',
  })
  dataType?: number;

  @property({
    type: 'number',
  })
  categoryType?: number;

  @property({
    type: 'string',
  })
  WasteCategory?: string;

  @property({
    type: 'string',
  })
  Date?: string;

  @property({
    type: 'string',
  })
  Type?: string;

  @property({
    type: 'string',
  })
  sapId?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  FuelType?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  EmpId?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  BTOrigin?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  Month?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  PurchaseOrder?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  HSNCode?: string | null;
  @property({
    type: 'any'
  })
  TotalSpent?: any;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  efKey?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  MaterialCategory?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  MaterialDescription?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  VendorCode?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  SupplierName?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  Plant?: string | null;


  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  BTDestination?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  DepatureDate?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  ArrivalDate?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  BTPNR?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  classType?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  btTitle?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  BTMode?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  ModeOfTransportation?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  WasteDescription?: string | null;

  @property({
    type: 'any'
  })
  Quantity?: any;

  @property({
    type: 'any'
  })
  ProductType?: any;
  @property({
    type: 'any'
  })
  MaterialNumber?: any;
  @property({
    type: 'any'
  })
  OriginLocation?: any;
  @property({
    type: 'any'
  })
  ContractType?: any;
  @property({
    type: 'any'
  })
  EmpType?: any;
  @property({
    type: 'any'
  })
  DOB?: any;
  @property({
    type: 'any'
  })
  Gender?: any;
  @property({
    type: 'any'
  })
  OfficeCity?: any;
  @property({
    type: 'any'
  })
  OfficeLocation?: any;
  @property({
    type: 'any'
  })
  InvoiceAmount?: any;
  @property({
    type: 'any'
  })
  DestinationLocation?: any;

  @property({
    type: 'any'
  })
  Currency?: any;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  UoM?: string | null;

  @property({
    type: 'number',
    itemType: 'number',
    required: false,
    mysql: {
      dataType: "BIGINT"
    },
    jsonSchema: {
      nullable: true,
    }
  })
  Distance?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier0_id?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier1_id?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier2_id?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier3_id?: number | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  fetched_on?: string | null;

  @property({
    type: 'number',
  })
  level?: number; // Stores the mapped value of Qty

  @property({
    type: 'number',
  })
  locationId?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<SapResponse>) {
    super(data);
  }
}

export interface SapResponseRelations {
  // describe navigational properties here
}

export type SapResponseWithRelations = SapResponse & SapResponseRelations;
