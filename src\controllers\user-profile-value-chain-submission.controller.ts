import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  HttpErrors,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  ValueChainSubmission,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileValueChainSubmissionController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/value-chain-submissions', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many ValueChainSubmission',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ValueChainSubmission)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<ValueChainSubmission>,
  ): Promise<ValueChainSubmission[]> {
    return this.userProfileRepository.valueChainSubmissions(id).find(filter);
  }

  @post('/user-profiles/{id}/value-chain-submissions', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(ValueChainSubmission)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ValueChainSubmission, {
            title: 'NewValueChainSubmissionInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) valueChainSubmission: Omit<ValueChainSubmission, 'id'>,
  ): Promise<ValueChainSubmission> {



    const {srfId, tier0_id, tier1_id, tier2_id, tier3_id, reporting_period, vendorId} = valueChainSubmission
    const whereFilter: any = {srfId, vendorId};

    if (tier0_id != null) whereFilter.tier0_id = tier0_id;
    if (tier1_id != null) whereFilter.tier1_id = tier1_id;
    if (tier2_id != null) whereFilter.tier2_id = tier2_id;
    if (tier3_id != null) whereFilter.tier3_id = tier3_id;

    let data = await this.userProfileRepository.valueChainSubmissions(id).find({where: whereFilter})
    if (data.length) {
      let isMatch = data.filter(item => item.reporting_period &&
        item.reporting_period.some(period => reporting_period && reporting_period.includes(period))
      );
      if (isMatch.length === 0) {
        return this.userProfileRepository.valueChainSubmissions(id).create(valueChainSubmission);

      } else {
        throw new HttpErrors.BadRequest('Data Already Exist');
      }
    } else {


      return this.userProfileRepository.valueChainSubmissions(id).create(valueChainSubmission);
    }

  }

  @patch('/user-profiles/{id}/value-chain-submissions-update', {
    responses: {
      '200': {
        description: 'UserProfile.ValueChainSubmission PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ValueChainSubmission, {partial: true}),
        },
      },
    })
    valueChainSubmission: Partial<ValueChainSubmission>,
    @param.query.object('where', getWhereSchemaFor(ValueChainSubmission)) where?: Where<ValueChainSubmission>,
  ): Promise<Count> {
    if (where) {
      return this.userProfileRepository.valueChainSubmissions(id).patch(valueChainSubmission, where);

    } else {
      throw new HttpErrors.BadRequest('Where is required');

    }
  }

  @del('/user-profiles/{id}/value-chain-submissions-delete', {
    responses: {
      '200': {
        description: 'UserProfile.ValueChainSubmission DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(ValueChainSubmission)) where?: Where<ValueChainSubmission>,
  ): Promise<Count> {
    if (where) {
      return this.userProfileRepository.valueChainSubmissions(id).delete(where);

    } else {
      throw new HttpErrors.BadRequest('Where is required');

    }

  }
}
