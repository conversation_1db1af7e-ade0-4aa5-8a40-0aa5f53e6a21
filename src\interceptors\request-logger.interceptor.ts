import {
  globalInterceptor,
  Interceptor,
  InvocationContext,
  Provider
} from '@loopback/core';

import {RequestContext, RestBindings} from '@loopback/rest';
import logger from '../utils/logger';

@globalInterceptor('logging', {tags: {name: 'RequestLogger'}})
export class RequestLoggerInterceptor implements Provider<Interceptor> {
  constructor() { }

  value(): Interceptor {
    return async (invocationCtx: InvocationContext, next) => {
      let requestCtx: RequestContext | undefined;
      let method: string = 'UNKNOWN';
      let originalUrl: string = 'UNKNOWN';
      let headers: any = {};
      let body: any = {};

      try {
        // Try to get the request context from the invocation context
        const ctx = await invocationCtx.get(RestBindings.Http.CONTEXT, {optional: true});
        if (ctx && typeof ctx === 'object' && 'request' in ctx) {
          requestCtx = ctx as RequestContext;
          const req = requestCtx.request;
          method = req.method;
          originalUrl = req.originalUrl;
          headers = req.headers;
          body = req.body;
        }
      } catch (error) {
        // If we can't get the request context, continue with default values
        logger.warn('Could not access request context in interceptor:', error);
      }

      const start = Date.now();
      let responseData: any;
      let statusCode = 200;

      try {
        responseData = await next();
      } catch (err: any) {
        statusCode = err.statusCode || 500;
        responseData = {error: err.message};
        throw err;
      } finally {
        const duration = Date.now() - start;
        logger.info({
          timestamp: new Date().toISOString(),
          method,
          url: originalUrl,
          headers,
          body,
          statusCode,
          response: responseData,
          duration: `${duration}ms`,
        });
      }
      return responseData;
    };
  }
}
