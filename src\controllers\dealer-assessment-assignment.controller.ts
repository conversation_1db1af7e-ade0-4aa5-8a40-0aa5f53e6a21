import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  HttpErrors,
  param,
  patch,
  post,
  put,
  requestBody,
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {Action, DealerAssessmentAssignment, DealerAuditorChecklistSubmission} from '../models';
import {
  ActionRepository,
  DealerAssessmentAssignmentRepository,
  DealerAuditorChecklistSubmissionRepository,
  UserProfileRepository,
  UserRoleAuthorizationRepository,
} from '../repositories';
import {VendorCodeRepository} from '../repositories/vendor-code.repository';
import {SqsService} from '../services/sqs-service.service';
import {UserProfileController} from './user-profile.controller';

export class DealerAssessmentAssignmentController {
  constructor(
    @repository(DealerAssessmentAssignmentRepository)
    public dealerAssessmentAssignmentRepository: DealerAssessmentAssignmentRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository,
    @repository(DealerAuditorChecklistSubmissionRepository)
    public dealerAuditorChecklistSubmissionRepository: DealerAuditorChecklistSubmissionRepository,
    @repository(UserRoleAuthorizationRepository)
    public userRoleAuthorizationRepository: UserRoleAuthorizationRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(VendorCodeRepository)
    public vendorCodeRepository: VendorCodeRepository,
    @inject('controllers.UserProfileController')
    public userProfileController: UserProfileController,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) { }

  @post('/dealer-assessment-assignments', {
    responses: {
      '200': {
        description: 'DealerAssessmentAssignment model instance',
        content: {'application/json': {schema: getModelSchemaRef(DealerAssessmentAssignment)}},
      },
    },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerAssessmentAssignment, {
            title: 'NewDealerAssessmentAssignment',
            exclude: ['id'],
          }),
        },
      },
    })
    dealerAssessmentAssignment: Omit<DealerAssessmentAssignment, 'id'>,
  ): Promise<DealerAssessmentAssignment> {
    return this.dealerAssessmentAssignmentRepository.create(dealerAssessmentAssignment);
  }

  @get('/dealer-assessment-assignments/count', {
    responses: {
      '200': {
        description: 'DealerAssessmentAssignment model count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.where(DealerAssessmentAssignment) where?: Where<DealerAssessmentAssignment>,
  ): Promise<Count> {
    return this.dealerAssessmentAssignmentRepository.count(where);
  }

  @get('/dealer-assessment-assignments', {
    responses: {
      '200': {
        description: 'Array of DealerAssessmentAssignment model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(DealerAssessmentAssignment, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(DealerAssessmentAssignment) filter?: Filter<DealerAssessmentAssignment>,
  ): Promise<DealerAssessmentAssignment[]> {
    return this.dealerAssessmentAssignmentRepository.find(filter);
  }

  @patch('/dealer-assessment-assignments', {
    responses: {
      '200': {
        description: 'DealerAssessmentAssignment PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerAssessmentAssignment, {partial: true}),
        },
      },
    })
    dealerAssessmentAssignment: DealerAssessmentAssignment,
    @param.where(DealerAssessmentAssignment) where?: Where<DealerAssessmentAssignment>,
  ): Promise<Count> {
    return this.dealerAssessmentAssignmentRepository.updateAll(dealerAssessmentAssignment, where);
  }

  @get('/dealer-assessment-assignments/{id}', {
    responses: {
      '200': {
        description: 'DealerAssessmentAssignment model instance',
        content: {
          'application/json': {
            schema: getModelSchemaRef(DealerAssessmentAssignment, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(DealerAssessmentAssignment, {exclude: 'where'}) filter?: FilterExcludingWhere<DealerAssessmentAssignment>
  ): Promise<DealerAssessmentAssignment> {
    return this.dealerAssessmentAssignmentRepository.findById(id, filter);
  }

  @patch('/dealer-assessment-assignments/{id}', {
    responses: {
      '204': {
        description: 'DealerAssessmentAssignment PATCH success',
      },
    },
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerAssessmentAssignment, {partial: true}),
        },
      },
    })
    dealerAssessmentAssignment: DealerAssessmentAssignment,
  ): Promise<void> {
    // Fetch the old assignment
    const oldAssignment = await this.dealerAssessmentAssignmentRepository.findById(id);
    await this.dealerAssessmentAssignmentRepository.updateById(id, dealerAssessmentAssignment);
    const isRescheduled = DateTime.fromISO(oldAssignment?.auditStartDate || '', {zone: 'utc'}).isValid && DateTime.fromISO(dealerAssessmentAssignment?.auditStartDate || '', {zone: 'utc'}).isValid && DateTime.fromISO(dealerAssessmentAssignment?.auditStartDate || '', {zone: 'utc'}).setZone('Asia/Calcutta').toFormat('dd-MM-yyyy') !== DateTime.fromISO(oldAssignment?.auditStartDate || '', {zone: 'utc'}).setZone('Asia/Calcutta').toFormat('dd-MM-yyyy')
    // Check if auditStartDate is being updated and is different
    if (isRescheduled) {
      // Fetch vendor/dealer info
      const vendorData = await this.vendorCodeRepository.findById(Number(oldAssignment.vendorId));
      // Fetch auditor info
      const auditorData = await this.userProfileController.filteredUP({where: {id: {inq: oldAssignment.assessors || []}}});
      // Fetch dealer SPOC
      const vendorSpoc = await this.userProfileController.filteredUP({where: {id: vendorData.userProfileId}});
      // Fetch head mail ids
      const headMailids = this.userProfileController.extractDealerHeadValidEmails(
        vendorData,
        ['aps', 'ao', 'sales', 'service'],
        ['areaManagerMailId', 'areaCommercialManagerMailId']
      );
      // Fetch section admin mail ids
      const roles = await this.userRoleAuthorizationRepository.execute(
        `SELECT * FROM UserRoleAuthorization
      WHERE roles IS NOT NULL
        AND JSON_LENGTH(roles) > 0
        AND JSON_CONTAINS(roles, ?, '$')`,
        [JSON.stringify([13])]
      );
      const headUserIds = roles.map((i: any) => i.user_id).filter((x: any) => x);
      const headUserData = await this.userProfileController.filteredUP({where: {id: {inq: headUserIds}}});
      const sectionAdminMailIds = headUserData.map((x: any) => x.email).filter((x: any) => x);
      // Compose email
      const subject = 'Rescheduling of My Sustainability Index (MSI) Calibration';
      const body = `<p>Dear ${auditorData.map((a: any) => a?.information?.empname).join(', ')} and ${vendorData.dealerName},</p>
<p>Greetings from TVS Motor Company,</p>
<p>This is to formally inform you that the MSI Calibration for the below-mentioned dealer has been rescheduled as per the revised details provided below:</p>
<p><strong>Dealer Details:</strong></p>
<ul>
  <li>Dealer Name: <strong>${vendorData.dealerName}</strong></li>
  <li>Dealer Code: <strong>${vendorData.code}</strong></li>
  <li>Location: <strong>${vendorData.dealerLocation}</strong></li>
</ul>
<p><strong>Revised Audit Schedule:</strong></p>
<ul>
  <li>Audit Type: <strong>MSI Calibration</strong></li>
  <li>Rescheduled Date: <strong>${DateTime.fromISO(dealerAssessmentAssignment?.auditStartDate || '', {zone: 'utc'}).setZone('Asia/Calcutta').toFormat('dd-MM-yyyy')}</strong></li>
  ${auditorData.map((item: any) => `
    <li>Auditor Name: <strong>${item?.information?.empname || 'NA'}</strong></li>
    <li>Auditor Contact No: <strong>${item?.information?.contactno || 'NA'}</strong></li>
  `).join('')}
</ul>
<p>We request the dealer team (Sales Manager / Works Manager / HR & Finance Team) to be available on the revised date and ensure readiness with all necessary documentation and access required for a smooth calibration process. Please ensure the availability of the following documents: Air & Water Consent Orders, Hazardous Waste Authorization, Shops & Establishment Licenses, Relevant Insurance Documents, HR and Finance Records</p>
<p>We also request that the auditor kindly make the necessary adjustments to their schedule and be fully prepared with all required tools and documentation for the revised audit date.</p>
<p>Should you have any queries or require assistance, please contact us at <strong><EMAIL></strong> and kindly copy <strong><EMAIL></strong> in your communication. Thank you for your understanding and cooperation.</p>
<p>Best regards,<br/>TVS Motor Company Limited</p>`;
      // Send email
      const dealerSpoc = vendorSpoc[0]?.email;

      this.sqsService.sendEmail(
        [...auditorData.map((x: any) => x.email).filter((x: any) => x), dealerSpoc],
        subject,
        body,
        [...headMailids, ...sectionAdminMailIds]
      );
    }

  }

  @put('/dealer-assessment-assignments/{id}', {
    responses: {
      '204': {
        description: 'DealerAssessmentAssignment PUT success',
      },
    },
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() dealerAssessmentAssignment: DealerAssessmentAssignment,
  ): Promise<void> {
    await this.dealerAssessmentAssignmentRepository.replaceById(id, dealerAssessmentAssignment);
  }

  @del('/dealer-assessment-assignments/{id}', {
    responses: {
      '204': {
        description: 'DealerAssessmentAssignment DELETE success',
      },
    },
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.dealerAssessmentAssignmentRepository.deleteById(id);
  }

  @post('/dealer-auditor-checklist-submissions-status')
  async getAssignment(@requestBody({
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            userId: {
              type: 'number'
            },
            archive: {type: 'boolean', nullable: true},

          },
          required: ['userId'],
        },
      },
    },
  })
  requestBody: {userId: number, archive: boolean}
  ): Promise<any> {
    const {userId, archive} = requestBody
    try {
      const found = await this.userProfileRepository.findById(userId)
      if (found && found.clientId) {
        const roleCheck = await this.userRoleAuthorizationRepository.find({where: {user_id: userId, userProfileId: found.clientId}})
        if (roleCheck && roleCheck.length && roleCheck.some(x => x.roles?.includes(19))) {
          const assignmentList = await this.userProfileRepository.dealerAssessmentAssignments(found.clientId).find({"include": [{"relation": "dealer", "scope": {"fields": {"information": true, "dealerCode": true}}}, {relation: "vendor"}, {relation: "form"}, {relation: "dealerAuditorChecklistSubmission"}]})
          if (assignmentList.length && assignmentList.some(x => x.assessors?.includes(userId))) {
            const assignedList = assignmentList.filter(x => x.assessors?.includes(userId) && (!x.dealerAuditorChecklistSubmission?.type || x?.dealerAuditorChecklistSubmission?.type === 22))
            return {status: 2, archiveData: archive ? assignmentList.filter(x => x.assessors?.includes(userId) && (x.dealerAuditorChecklistSubmission && x.dealerAuditorChecklistSubmission?.type === 1)) : [], data: assignedList.map(({form, ...x}: any) => ({...x, checklistTitle: form?.title || '', maskId: 'MSI-' + (x?.vendor?.code || 'NA') + '-' + DateTime.fromISO(x.created_on, {zone: 'Asia/Calcutta'}).toFormat('ddMMyyyy')})), message: 'Checklist Found'}
          } else {
            return {status: 1, data: [], message: 'Checklist is not assigned'}
          }

        } else {
          return {status: 0, data: [], message: 'You are not authorized as MSI Dealer Accessor'}
        }
      } else {
        return {status: 0, data: [], message: 'Invalid Dealer'}
      }

    } catch (e) {
      return {status: false, data: [], message: e}
    }

  }

  @del('/dealer-assignment-calibration-action/{id}/cascade', {
    responses: {
      '200': {
        description: 'Delete counts for assignment and related records',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                checklistSubmissionsCount: {type: 'number'},
                actionsCount: {type: 'number'},
                assignmentCount: {type: 'number'}
              }
            }
          }
        }
      },
      '404': {
        description: 'Dealer Assessment Assignment not found',
      },
      '500': {
        description: 'Internal Server Error',
      },
    },
  })
  async deleteSupplierAssessmentAssignmentCascade(
    @param.path.number('id') id: number,
  ): Promise<{checklistSubmissionsCount: number; actionsCount: number; assignmentCount: number}> {
    try {
      // First check if the assignment exists
      const assignment = await this.dealerAssessmentAssignmentRepository.findById(id);
      if (!assignment) {
        throw new HttpErrors.NotFound('Dealer Assessment Assignment not found');
      }

      // Delete related dealer auditor checklist submissions and get count
      const checklistSubmissionsCount = (await this.dealerAuditorChecklistSubmissionRepository.deleteAll({
        dealerAssessmentAssignmentId: id
      } as Where<DealerAuditorChecklistSubmission>)).count;

      // Delete related actions and get count
      const actionsCount = (await this.actionRepository.deleteAll({
        appId: id
      } as Where<Action>)).count;

      // Finally delete the supplier assessment assignment
      await this.dealerAssessmentAssignmentRepository.deleteById(id);

      return {
        checklistSubmissionsCount,
        actionsCount,
        assignmentCount: 1 // Since we deleted one assignment
      };
    } catch (error) {
      if (error.code === 'ENTITY_NOT_FOUND') {
        throw new HttpErrors.NotFound('Dealer Assessment Assignment not found');
      }
      throw new HttpErrors.InternalServerError('Error occurred while deleting records');
    }
  }
}
