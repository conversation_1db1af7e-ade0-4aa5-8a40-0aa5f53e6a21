import {AuthenticationComponent, registerAuthenticationStrategy} from '@loopback/authentication';
import {
  JWTAuthenticationComponent,
  UserServiceBindings
} from '@loopback/authentication-jwt';
import {BootMixin} from '@loopback/boot';
import {ApplicationConfig} from '@loopback/core';
import {RepositoryMixin} from '@loopback/repository';
import {RestApplication, RestBindings} from '@loopback/rest';
import {
  RestExplorerBindings,
  RestExplorerComponent,
} from '@loopback/rest-explorer';
import {ServiceMixin} from '@loopback/service-proxy';
import multer from 'multer';
import path from 'path';
import {SapFuelController} from './controllers';
import {MysqlDataSource} from './datasources';
import {RequestLoggerInterceptor} from './interceptors/request-logger.interceptor';
import {FILE_UPLOAD_SERVICE, STORAGE_DIRECTORY} from './keys';
import {SapFuelRepository} from './repositories';
import {MySequence} from './sequence';
import {HelperProvider} from './services';
import {SapService} from './services/sap.service';
import {CognitoJwtAuthenticationStrategy} from './strategies/cognito-jwt-strategy';
import {CognitoTVSJwtAuthenticationStrategy} from './strategies/cognito-tvs-strategy';

export {ApplicationConfig};

export class Apiv1Application extends BootMixin(
  ServiceMixin(RepositoryMixin(RestApplication)),
) {
  constructor(options: ApplicationConfig = {}) {

    super(options);

    // Set up the custom sequence
    this.sequence(MySequence);

    // Set up default home page
    this.static('/', path.join(__dirname, '../public'));
    this.static('/files', path.join(__dirname, '../.sandbox'));
    // Winston logger
    this.interceptor(RequestLoggerInterceptor);
    // Customize @loopback/rest-explorer configuration here
    this.configure(RestExplorerBindings.COMPONENT).to({
      path: '/explorer',
    });
    this.component(RestExplorerComponent);
    // Mount authentication system
    this.component(AuthenticationComponent);
    // Mount jwt component
    this.component(JWTAuthenticationComponent);
    // Bind datasource
    registerAuthenticationStrategy(this, CognitoJwtAuthenticationStrategy);
    registerAuthenticationStrategy(this, CognitoTVSJwtAuthenticationStrategy);
    this.dataSource(MysqlDataSource, UserServiceBindings.DATASOURCE_NAME);

    this.configureFileUpload(options.fileStorageDirectory);

    this.projectRoot = __dirname;
    // Customize @loopback/boot Booter Conventions here
    this.bootOptions = {
      controllers: {
        // Customize ControllerBooter Conventions here
        dirs: ['controllers'],
        extensions: ['.controller.js'],
        nested: true,
      },
    };

    this.controller(SapFuelController); // Register the S3Controller
    this.bind('services.S3Service').toClass(SapService);
    this.bind('repositories.S3Repository').toClass(SapFuelRepository);
    this.bind('services.HelperProvider').toProvider(HelperProvider);
    this.bind(RestBindings.REQUEST_BODY_PARSER_OPTIONS).to({
      limit: '30mb', // Adjust as needed
    });
  }

  protected configureFileUpload(destination?: string) {
    // Upload files to `dist/.sandbox` by default
    destination = destination ?? path.join(__dirname, '../public/docs');
    this.bind(STORAGE_DIRECTORY).to(destination);
    const multerOptions: multer.Options = {
      storage: multer.diskStorage({
        destination,
        // Use the original file name as is
        filename: (req, file, cb) => {
          cb(null, new Date().getTime() + file.originalname);
        },
      }),
    };
    // Configure the file upload service with multer options
    this.configure(FILE_UPLOAD_SERVICE).to(multerOptions);
  }
}
