import {belongsTo, Entity, model, property} from '@loopback/repository';
import {FormCollection} from './form-collection.model';
import {LocationOne} from './location-one.model';
import {LocationTwo} from './location-two.model';

@model()
export class AssignDcfEntityUser extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;


  @property({
    type: 'array',
    itemType: 'number',
  })
  reporter_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  reviewer_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  approver_ids?: number[];

  @property({
    type: 'string',
  })
  start_date?: string;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  end_date?: string | null;

  @property({
    type: 'boolean',
    jsonSchema: {
      nullable: true,
    }
  })
  isAutoApproval?: boolean | null;

  @property({
    type: 'number',
  })
  frequency?: number;

  @property({
    type: 'any',
  })
  standard?: any;

  @property({
    type: 'number',
  })
  level?: number;

  @property({
    type: 'number',
  })
  locationId?: number;
  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;

  @property({
    type: 'number',
  })
  modified_by?: number;

  @property({
    type: 'any',
  })
  comments?: any;

  @property({
    type: 'number',
  })
  type?: number;
  @property({
    type: 'number',
  })
  entityAssId?: number;
  @property({
    type: 'number',
  })
  tier0_id?: number;
  @property({
    type: 'number',
  })
  tier3_id?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  @belongsTo(() => FormCollection)
  dcfId: number;

  @belongsTo(() => LocationOne, {name: 'lone'})
  tier1_id: number;

  @belongsTo(() => LocationTwo, {name: 'ltwo'})
  tier2_id: number;

  constructor(data?: Partial<AssignDcfEntityUser>) {
    super(data);
  }
}

export interface AssignDcfEntityUserRelations {
  // describe navigational properties here
}

export type AssignDcfEntityUserWithRelations = AssignDcfEntityUser & AssignDcfEntityUserRelations;
