import {belongsTo, Entity, model, property} from '@loopback/repository';
import {VendorCode} from './vendor-code.model';

@model()
export class AuditorAssignmentSubmission extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;



  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'number',
  })
  reject?: number;

  @property({
    type: 'string',
    mysql: {
      dataType: 'LONGTEXT'
    }
  })
  response?: string;





  @property({
    type: 'any',
  })
  rejectionComments?: any;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  created_on?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  modified_on?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  submitted_on?: string | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  submitted_by?: number | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  rejected_on?: string | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  rejected_by?: number | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  approved_on?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  reviewed_on?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  second_review_on?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  auto_reviewed_on?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  auto_second_review_on?: string | null;

  @property({
    type: 'boolean',
    jsonSchema: {
      nullable: true,
    }
  })
  auto_reviewed?: boolean | null;

  @property({
    type: 'boolean',
    jsonSchema: {
      nullable: true,
    }
  })
  auto_second_review?: boolean | null;

  @property({
    type: 'any',
  })
  approverComments?: any;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  approved_by?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  reviewed_by?: number | null;
  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  second_review_by?: number | null;
  @property({
    type: 'any',

    mysql: {
      dataType: 'LONGTEXT',
    },
    jsonSchema: {
      type: 'array',
      items: {
        type: 'object',
      },
      nullable: true,
    }

  })
  reportMailStatus?: any[] | null;
  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  created_by?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  modified_by?: number | null;


  @property({
    type: 'any'
  })
  auditorMSIScore?: any

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  status?: number | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  vendorCode?: string | null;

  @property({
    type: 'number',
  })
  userProfileId?: number;


  @property({
    type: 'string',
  })
  supplierAssessmentAssignmentId?: string;

  @belongsTo(() => VendorCode)
  vendorId: number;

  constructor(data?: Partial<AuditorAssignmentSubmission>) {
    super(data);
  }
}

export interface AuditorAssignmentSubmissionRelations {
  // describe navigational properties here
}

export type AuditorAssignmentSubmissionWithRelations = AuditorAssignmentSubmission & AuditorAssignmentSubmissionRelations;
