import {UserRepository} from '@loopback/authentication-jwt';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
  response
} from '@loopback/rest';
import {
  AssignSrfEntityUser,
  UserProfile,
} from '../models';
import {AssignSrfEntityRepository, AssignSrfEntityUserRepository, UserProfileRepository, VendorCodeRepository} from '../repositories';
import {SqsService} from '../services/sqs-service.service';

export class UserProfileAssignSrfEntityUserController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
    @repository(AssignSrfEntityUserRepository) protected assignSrfEntityUserRepository: AssignSrfEntityUserRepository,
    @repository(AssignSrfEntityRepository) protected assignSrfEntityRepository: AssignSrfEntityRepository,
    @repository(UserRepository) protected userRepository: UserRepository,
    @repository(VendorCodeRepository) protected vendorCodeRepository: VendorCodeRepository,
    @inject('services.SqsService') protected sqsService: SqsService,
  ) { }

  @get('/user-profiles/{id}/assign-srf-entity-users', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many AssignSrfEntityUser',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssignSrfEntityUser)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<AssignSrfEntityUser>,
  ): Promise<AssignSrfEntityUser[]> {
    return this.userProfileRepository.assignSrfEntityUsers(id).find(filter);
  }

  @post('/user-profiles/{id}/assign-srf-entity-users', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssignSrfEntityUser)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignSrfEntityUser, {
            title: 'NewAssignSrfEntityUserInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) assignSrfEntityUser: Omit<AssignSrfEntityUser, 'id'>,
  ): Promise<AssignSrfEntityUser> {
    return this.userProfileRepository.assignSrfEntityUsers(id).create(assignSrfEntityUser);
  }

  @patch('/user-profiles/{id}/assign-srf-entity-users', {
    responses: {
      '200': {
        description: 'UserProfile.AssignSrfEntityUser PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignSrfEntityUser, {partial: true}),
        },
      },
    })
    assignSrfEntityUser: Partial<AssignSrfEntityUser>,
    @param.query.object('where', getWhereSchemaFor(AssignSrfEntityUser)) where?: Where<AssignSrfEntityUser>,
  ): Promise<Count> {
    return this.userProfileRepository.assignSrfEntityUsers(id).patch(assignSrfEntityUser, where);
  }

  @del('/user-profiles/{id}/assign-srf-entity-users', {
    responses: {
      '200': {
        description: 'UserProfile.AssignSrfEntityUser DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(AssignSrfEntityUser)) where?: Where<AssignSrfEntityUser>,
  ): Promise<Count> {
    return this.userProfileRepository.assignSrfEntityUsers(id).delete(where);
  }
  @post('/user-profiles/{id}/assign-srf-entity-users-custom')
  @response(204, {
    description: 'AssignDcfEntityUser PATCH success',
  })
  async updateByIdCustom(
    @param.path.number('id') uid: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignSrfEntityUser, {partial: true}),
        },
      },
    })
    assignDcfEntityUser: AssignSrfEntityUser,
  ): Promise<any> {
    try {
      const {entityAssId, id, srfId, type, dealerType} = assignDcfEntityUser
      if (entityAssId && id) {
        await this.assignSrfEntityUserRepository.updateById(id, assignDcfEntityUser)
        return {result: 1, message: 'Updated Successfully', data: {}}
      } else if (!entityAssId && !id && srfId && type) {
        let found = await this.assignSrfEntityRepository.findOne({where: {userProfileId: uid, srfId: srfId, type}})
        if (!found) {
          const createdData = await this.assignSrfEntityRepository.create({userProfileId: uid, tier0_ids: [0], srfId: srfId, tier1_ids: [], tier2_ids: [], tier3_ids: [], type, created_by: assignDcfEntityUser.created_by, created_on: assignDcfEntityUser.created_on})
          const data = await this.userProfileRepository.assignSrfEntityUsers(uid).create({...assignDcfEntityUser, entityAssId: createdData.id})
          return {result: 2, message: 'Created Successfully', data, entityData: createdData}
        } else {
          return {result: 0, message: 'Something went wrong while creating/updating assignment', data: {}}


        }
      } else if (entityAssId && !id) {

        const anotherData = await this.userProfileRepository.assignSrfEntityUsers(uid).create({...assignDcfEntityUser, entityAssId: entityAssId})
        return {result: 3, message: 'Created Successfully', data: anotherData}
        // let assignedData = await this.userProfileRepository.assignSrfEntityUsers(uid).find({where: {srfId: srfId, type, entityAssId, dealerType}})
        // if ((assignedData.length && assignedData.every(item => item.end_date) || !assignedData.length)) {
        //   const anotherData = await this.userProfileRepository.assignSrfEntityUsers(uid).create({...assignDcfEntityUser, entityAssId: entityAssId})
        //   return {result: 3, message: 'Created Successfully', data: anotherData}
        // } else {
        //   console.log(assignedData)
        //   return {result: 0, message: 'Something went wrong while  assignment', data: {}}

        // }


      } else {
        return {result: 0, message: 'Something went wrong while creating/updating assignment', data: {}}

      }
    } catch (e) {
      return {result: 0, message: 'Something went wrong', data: {}}

    }

  }

  @post('/user-profiles/{id}/assign-srf-entity-users-bulk-custom')
  @response(204, {
    description: 'AssignDcfEntityUser PATCH success',
  })
  async bulkSRFAddition(
    @param.path.number('id') uid: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignSrfEntityUser, {partial: true}),
        },
      },
    })
    assignDcfEntityUser: AssignSrfEntityUser,
  ): Promise<any> {
    try {
      const {entityAssId, id, srfId, type, dealerType, reporter_ids} = assignDcfEntityUser
      if (entityAssId && id) {
        await this.assignSrfEntityUserRepository.updateById(id, assignDcfEntityUser)
        return {result: 1, message: 'Updated Successfully', data: []}
      } else if (!entityAssId && !id && srfId && type && reporter_ids?.length) {
        let found = await this.assignSrfEntityRepository.findOne({where: {userProfileId: uid, srfId: srfId, type}})
        if (!found) {
          const createdData = await this.assignSrfEntityRepository.create({userProfileId: uid, tier0_ids: [0], srfId: srfId, tier1_ids: [], tier2_ids: [], tier3_ids: [], type, created_by: assignDcfEntityUser.created_by, created_on: assignDcfEntityUser.created_on})

          const promises = reporter_ids.map((item) =>
            this.userProfileRepository.assignSrfEntityUsers(uid).create({
              ...assignDcfEntityUser,
              reporter_ids: [item],
              entityAssId: createdData.id
            })
          );


          const resultArray = await Promise.all(promises);

          // Send LCA assignment emails for newly created records when srfId is 86
          if (srfId === 86) {
            // await this.sendLCAAssignmentEmail(resultArray);
          }

          return {result: 2, message: 'Created Successfully', data: resultArray, entityData: createdData}
        } else {
          return {result: 0, message: 'Something went wrong while creating/updating assignment', data: []}


        }
      } else if (entityAssId && !id && reporter_ids?.length) {

        const promises = reporter_ids.map((item) =>
          this.userProfileRepository.assignSrfEntityUsers(uid).create({
            ...assignDcfEntityUser,
            reporter_ids: [item],
            entityAssId: entityAssId
          })
        );


        const resultArray = await Promise.all(promises);

        // Send LCA assignment emails for newly created records when srfId is 86
        if (srfId === 86) {
          // await this.sendLCAAssignmentEmail(resultArray);
        }

        return {result: 3, message: 'Created Successfully', data: resultArray}



      } else {
        return {result: 0, message: 'Something went wrong while creating/updating assignment', data: {}}

      }
    } catch (e) {
      return {result: 0, message: 'Something went wrong', data: {}}

    }

  }

  /**
   * Send LCA assignment email to suppliers when srfId is 86 and records are newly created
   */
  private async sendLCAAssignmentEmail(createdRecords: AssignSrfEntityUser[]): Promise<void> {
    try {
      for (const record of createdRecords) {
        if (record.srfId === 86 && record.reporter_ids?.length) {
          // Process each vendor ID from reporter_ids (reporter_ids contains vendor-code IDs)
          for (const vendorId of record.reporter_ids) {
            try {
              // Get vendor details using vendor-code ID
              const vendor = await this.vendorCodeRepository.findById(vendorId);

              if (vendor && vendor.userProfileId) {
                // Get supplier SPOC email from user profile
                const userProfile = await this.userProfileRepository.findById(vendor.userProfileId);
                const user = await this.userRepository.findById(userProfile?.userId);
                const supplierSpoc = user?.email;

                // Get additional contact emails from vendor
                const otherContacts = [
                  vendor.supplierEmail2,
                  vendor.supplierEmail3
                ].filter(email => email && this.isValidEmail(email));

                // Combine all email recipients
                const allRecipientEmails = [supplierSpoc, ...otherContacts].filter(email => email);

                if (allRecipientEmails.length > 0) {
                  const supplierName = vendor.supplierName || 'Supplier';
                  const supplierCode = vendor.code || 'N/A';

                  const subject = `Life Cycle Assessment Data Collection Form Assignment – ${supplierName} (${supplierCode})`;

                  const emailBody = `
                    <p>Dear ${supplierName},</p>
<div>Receiver : ${allRecipientEmails.join(', ')}</div>
                    <p>Greetings from Navigos,</p>

                    <p>As part of our ongoing commitment to integrating sustainability into product development, we request that you complete the Life Cycle Assessment (LCA) form for the part numbers you are supplying to TVS Motor Company.</p>

                    <p>Your LCA form has now been assigned and made live on the Navigos Sustainability Platform. We kindly request that you to complete the submission</p>

                    <p><strong>What You Need to Do:</strong></p>
                    <ul>
                      <li>Log in to the platform at <a href="https://tvsmotor-supplier.eisqr.com/">https://tvsmotor-supplier.eisqr.com/</a> using your assigned credentials</li>
                      <li>Navigate to the LCA Forms section under the "My Actions" tab</li>
                      <li>Complete the LCA form(s) for each assigned part number and submit before the due date</li>
                      <li>Ensure accuracy in the information provided, and upload relevant supporting documentation such as process data, energy/water/waste inputs, and emission data, wherever applicable</li>
                    </ul>

                    <p>The information you provide will be instrumental in assessing the environmental footprint of the parts supplied and strengthening our collective sustainability efforts.</p>

                    <p>For any questions or support while filling the form, please contact <a href="mailto:<EMAIL>"><EMAIL></a> and kindly copy <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

                    <p>We appreciate your cooperation and valuable contribution to our sustainability journey.</p>

                    <p>Warm regards,<br/>
                    TVS Motor Company Limited</p>

                    <p><em>This is an automated message. Please do not reply to this email.</em></p>

                    <p>Regards,<br/>
                    NAVIGOS</p>
                  `;
                  // axios.post('https://api.eisqr.com/post-email', {to: ['<EMAIL>'], subject, body: emailBody});
                  // Send email to all recipient emails
                  // await this.sqsService.sendEmail(
                  //   allRecipientEmails,
                  //   subject,
                  //   emailBody,
                  //   ['<EMAIL>'] // CC
                  // ).then((info) => {
                  //   console.log(`LCA assignment email sent to ${allRecipientEmails.join(', ')} for supplier ${supplierName} (${supplierCode})`);
                  //   return info;
                  // }).catch((err) => {
                  //   console.error(`Error sending LCA assignment email to ${allRecipientEmails.join(', ')} for supplier ${supplierName}:`, err);
                  //   return err;
                  // });
                }
              }
            } catch (vendorError) {
              console.error(`Error processing vendor ID ${vendorId}:`, vendorError);
            }
          }
        }
      }
    } catch (error) {
      console.error('Error in sendLCAAssignmentEmail:', error);
    }
  }

  /**
   * Helper function to validate email addresses
   */
  private isValidEmail(email: any): boolean {
    if (!email?.trim()) return false; // Returns false for null, undefined, or empty string
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  }


}
