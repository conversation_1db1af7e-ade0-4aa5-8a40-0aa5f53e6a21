Indicator 165 classification: type=0, source=1, isStandalone=true, isDerived=false, formula="(DPA0336* (S1-T2*-G1-GS1-I1*)"
Indicator 165: STANDALONE, formula: "(DPA0336* (S1-T2*-G1-GS1-I1*)", dcfIds: [11, 11, 11, 11, 11, 11, 11, 0, 11]
Indicator 166 found in DB: true
Indicator 166 DCF extraction: {
  newDataPointsCount: 4,
  dcfIds: [ 10, 10, 10, 10 ],
  sampleData1: {
    unit: 'Type of gas refilled',
    desc: '',
    suffix: 'DPA0136',
    tags: [ 'GRI 305-1a', 'BRSR Principle 6: Essential Indicator 6' ],
    datasource: 10,
    type: { name: 'Qualitative' }
  },
  fullNewDataPoints: NewDataPoint {
    id: 1193,
    title: 'Type of gas refilled',
    suffix: 'DPA0136',
    data1: [ [Object] ],
    data2: null,
    extra: null,
    created: null,
    newMetricId: 166,
    order: null,
    cloneMetricId: null,
    cloneDataPointId: null
  }
}
Indicator 166 formula sources: {
  fromDataPoint: '',
  fromIndicator: '',
  fromData1: '',
  finalFormula: '',
  indicatorKeys: [
    'id',            'title',
    'suffix',        'data1',
    'data2',         'extra',
    'created',       'newTopicId',
    'order',         'cloneMetricId',
    'tag',           'cloneTopicId',
    'newDataPoints'
  ]
}
Indicator 166 classification: type=0, source=1, isStandalone=true, isDerived=false, formula=""
Indicator 166: STANDALONE, formula: "", dcfIds: [10, 10, 10, 10]
Indicator 167 found in DB: true
Indicator 167 DCF extraction: {
  newDataPointsCount: 9,
  dcfIds: [
    15, 15, 15, 15, 15,
    15, 15, 15, 15
  ],
  sampleData1: {
    unit: 'KM/Miles',
    desc: '',
    suffix: 'DPA0339',
    tags: [ 'GRI 305-1' ],
    datasource: 15,
    type: { name: 'Quantitative' }
  },
  fullNewDataPoints: NewDataPoint {
    id: 1197,
    title: 'Unit of distance measured',
    suffix: 'DPA0339',
    data1: [ [Object] ],
    data2: null,
    extra: null,
    created: null,
    newMetricId: 167,
    order: null,
    cloneMetricId: null,
    cloneDataPointId: null
  }
}
Indicator 167 formula sources: {
  fromDataPoint: '',
  fromIndicator: '',
  fromData1: '',
  finalFormula: '',
  indicatorKeys: [
    'id',            'title',
    'suffix',        'data1',
    'data2',         'extra',
    'created',       'newTopicId',
    'order',         'cloneMetricId',
    'tag',           'cloneTopicId',
    'newDataPoints'
  ]
}
Indicator 167 classification: type=0, source=1, isStandalone=true, isDerived=false, formula=""
Indicator 167: STANDALONE, formula: "", dcfIds: [15, 15, 15, 15, 15, 15, 15, 15, 15]
🔍 Indicator 165 classification: STANDALONE (formula: "(DPA0336* (S1-T2*-G1-GS1-I1*)")
Found 3 standalone indicators to process
Standalone indicators to process: [
  {
    id: 165,
    title: 'Scope 1: Stationary Combustion Emission',
    dcfIds: [ 11, 0 ],
    hasData1: true
  },
  {
    id: 166,
    title: 'Scope 1: Fugitive Emission',
    dcfIds: [ 10 ],
    hasData1: true
  },
  {
    id: 167,
    title: 'Emission from the owned or controlled vehicles of the organization',
    dcfIds: [ 15 ],
    hasData1: true
  }
]
processCustomMetricsEnhanced returned 3072 results
🔍 Indicator 165 was processed as standalone - found 190 results
🔍 Hosur results for indicator 165:
  Apr-2024: 785.545
  Apr-2024: 3.798
  Apr-2024: 222.907
  Apr-2024: 65.445
  Apr-2024: 0.000
  May-2024: 795.428
  May-2024: 0.000
  May-2024: 248.233
  May-2024: 18.069
  May-2024: 1.916
  Jun-2024: 723.122
  Jun-2024: 1.484
  Jun-2024: 241.092
  Jun-2024: 32.854
  Jun-2024: 9.103
  Jul-2024: 783.593
  Jul-2024: 8.989
  Jul-2024: 217.228
  Jul-2024: 10.646
  Jul-2024: 2.662
  Aug-2024: 727.079
  Aug-2024: 2.216
  Aug-2024: 199.764
  Aug-2024: 0.102
  Aug-2024: 0.000
  Sep-2024: 734.633
  Sep-2024: 4.503
  Sep-2024: 244.715
  Sep-2024: 56.669
  Sep-2024: 6.707
  Oct-2024: 689.836
  Oct-2024: 6.502
  Oct-2024: 256.196
  Oct-2024: 2.821
  Oct-2024: 26.295
  Nov-2024: 749.267
  Nov-2024: 3.369
  Nov-2024: 249.727
  Nov-2024: 0.000
  Dec-2024: 645.642
  Dec-2024: 3.798
  Dec-2024: 216.721
  Dec-2024: 86.117
  Dec-2024: 10.886
  Jan-2025: 870.306
  Jan-2025: 13.840
  Jan-2025: 35.305
  Feb-2025: 39.607
  Feb-2025: 10.699
  Feb-2025: 880.701
  Mar-2025: 808.827
  Mar-2025: 1.996
  Mar-2025: 8.496
  Jan-2025: 2.140
  Jan-2025: 241.067
  Feb-2025: 2.243
  Feb-2025: 252.828
  Mar-2025: 3.560
  Mar-2025: 225.969
  Nov-2024: 3.362
Processed 3072 standalone results
Found 2 derived indicators to process
Processing derived indicator 162: 162 : Total Scope 1 emissions
Formula: MT165+MT166+MT167
Dependencies from formula: [ 165, 166, 167 ]
  Processing dependency 165 first...
Processing derived indicator 165: Scope 1: Stationary Combustion Emission
Formula: (DPA0336* (S1-T2*-G1-GS1-I1*)
Dependencies from formula: []
Processing derived indicator 165: Scope 1: Stationary Combustion Emission
Formula: (DPA0336* (S1-T2*-G1-GS1-I1*)
Dependencies from formula: []
Available data keys: [
  'Apr-2024|166|2|165', 'May-2024|166|2|165', 'Jun-2024|166|2|165',
  'Jul-2024|166|2|165', 'Aug-2024|166|2|165', 'Sep-2024|166|2|165',
  'Oct-2024|166|2|165', 'Nov-2024|166|2|165', 'Dec-2024|166|2|165',
  'Jan-2025|166|2|165', 'Feb-2025|166|2|165', 'Apr-2024|167|2|165',
  'May-2024|167|2|165', 'Jun-2024|167|2|165', 'Jul-2024|167|2|165',
  'Aug-2024|167|2|165', 'Sep-2024|167|2|165', 'Oct-2024|167|2|165',
  'Nov-2024|167|2|165', 'Dec-2024|167|2|165', 'Jan-2025|167|2|165',
  'Feb-2025|167|2|165', 'Mar-2025|167|2|165', 'Apr-2024|130|2|165',
  'May-2024|130|2|165', 'Jun-2024|130|2|165', 'Jul-2024|130|2|165',
  'Aug-2024|130|2|165', 'Sep-2024|130|2|165', 'Oct-2024|130|2|165',
  'Nov-2024|130|2|165', 'Dec-2024|130|2|165', 'Jan-2025|130|2|165',
  'Feb-2025|130|2|165', 'Mar-2025|130|2|165', 'Apr-2024|131|2|165',
  'Apr-2024|132|2|165', 'May-2024|132|2|165', 'Jun-2024|132|2|165',
  'Jul-2024|132|2|165', 'Aug-2024|132|2|165', 'Sep-2024|132|2|165',
  'Oct-2024|132|2|165', 'Nov-2024|132|2|165', 'Dec-2024|132|2|165',
  'Jan-2025|132|2|165', 'Feb-2025|132|2|165', 'Mar-2025|132|2|165',
  'May-2024|131|2|165', 'Jun-2024|131|2|165', 'Jul-2024|131|2|165',
  'Aug-2024|131|2|165', 'Sep-2024|131|2|165', 'Oct-2024|131|2|165',
  'Nov-2024|131|2|165', 'Dec-2024|131|2|165', 'Jan-2025|131|2|165',
  'Feb-2025|131|2|165', 'Mar-2025|131|2|165', 'Apr-2024|229|3|165',
  'May-2024|229|3|165', 'Jun-2024|229|3|165', 'Jul-2024|229|3|165',
  'Aug-2024|229|3|165', 'Sep-2024|229|3|165', 'Oct-2024|229|3|165',
  'Nov-2024|229|3|165', 'Dec-2024|229|3|165', 'Jan-2025|229|3|165',
  'Feb-2025|229|3|165', 'Mar-2025|229|3|165', 'Apr-2024|130|2|166',
  'May-2024|130|2|166', 'Aug-2024|130|2|166', 'Sep-2024|130|2|166',
  'Jan-2025|130|2|166', 'Nov-2024|130|2|166', 'Oct-2024|130|2|166',
  'Jun-2024|130|2|166', 'Dec-2024|130|2|166', 'Jul-2024|130|2|166',
  'Apr-2024|131|2|166', 'May-2024|131|2|166', 'Jun-2024|131|2|166',
  'Jul-2024|131|2|166', 'Aug-2024|131|2|166', 'Sep-2024|131|2|166',
  'Oct-2024|131|2|166', 'Nov-2024|131|2|166', 'Dec-2024|131|2|166',
  'Jan-2025|131|2|166', 'Feb-2025|131|2|166', 'Apr-2024|132|2|166',
  'Jun-2024|132|2|166', 'May-2024|132|2|166', 'Jul-2024|132|2|166',
  'Aug-2024|132|2|166', 'Sep-2024|132|2|166', 'Oct-2024|132|2|166',
  'Nov-2024|132|2|166',
  ... 36 more items
]
Target frequency: Infinity
Checking entity combinations for indicator 165 with dependencies: []
Dependencies needed: []
Found 0 entities with available data for indicator 165
Entity-period combinations found: 0
Processing derived indicator 162: 162 : Total Scope 1 emissions
Formula: MT165+MT166+MT167
Dependencies from formula: [ 165, 166, 167 ]
Available data keys: [
  'Apr-2024|166|2|165', 'May-2024|166|2|165', 'Jun-2024|166|2|165',
  'Jul-2024|166|2|165', 'Aug-2024|166|2|165', 'Sep-2024|166|2|165',
  'Oct-2024|166|2|165', 'Nov-2024|166|2|165', 'Dec-2024|166|2|165',
  'Jan-2025|166|2|165', 'Feb-2025|166|2|165', 'Apr-2024|167|2|165',
  'May-2024|167|2|165', 'Jun-2024|167|2|165', 'Jul-2024|167|2|165',
  'Aug-2024|167|2|165', 'Sep-2024|167|2|165', 'Oct-2024|167|2|165',
  'Nov-2024|167|2|165', 'Dec-2024|167|2|165', 'Jan-2025|167|2|165',
  'Feb-2025|167|2|165', 'Mar-2025|167|2|165', 'Apr-2024|130|2|165',
  'May-2024|130|2|165', 'Jun-2024|130|2|165', 'Jul-2024|130|2|165',
  'Aug-2024|130|2|165', 'Sep-2024|130|2|165', 'Oct-2024|130|2|165',
  'Nov-2024|130|2|165', 'Dec-2024|130|2|165', 'Jan-2025|130|2|165',
  'Feb-2025|130|2|165', 'Mar-2025|130|2|165', 'Apr-2024|131|2|165',
  'Apr-2024|132|2|165', 'May-2024|132|2|165', 'Jun-2024|132|2|165',
  'Jul-2024|132|2|165', 'Aug-2024|132|2|165', 'Sep-2024|132|2|165',
  'Oct-2024|132|2|165', 'Nov-2024|132|2|165', 'Dec-2024|132|2|165',
  'Jan-2025|132|2|165', 'Feb-2025|132|2|165', 'Mar-2025|132|2|165',
  'May-2024|131|2|165', 'Jun-2024|131|2|165', 'Jul-2024|131|2|165',
  'Aug-2024|131|2|165', 'Sep-2024|131|2|165', 'Oct-2024|131|2|165',
  'Nov-2024|131|2|165', 'Dec-2024|131|2|165', 'Jan-2025|131|2|165',
  'Feb-2025|131|2|165', 'Mar-2025|131|2|165', 'Apr-2024|229|3|165',
  'May-2024|229|3|165', 'Jun-2024|229|3|165', 'Jul-2024|229|3|165',
  'Aug-2024|229|3|165', 'Sep-2024|229|3|165', 'Oct-2024|229|3|165',
  'Nov-2024|229|3|165', 'Dec-2024|229|3|165', 'Jan-2025|229|3|165',
  'Feb-2025|229|3|165', 'Mar-2025|229|3|165', 'Apr-2024|130|2|166',
  'May-2024|130|2|166', 'Aug-2024|130|2|166', 'Sep-2024|130|2|166',
  'Jan-2025|130|2|166', 'Nov-2024|130|2|166', 'Oct-2024|130|2|166',
  'Jun-2024|130|2|166', 'Dec-2024|130|2|166', 'Jul-2024|130|2|166',
  'Apr-2024|131|2|166', 'May-2024|131|2|166', 'Jun-2024|131|2|166',
  'Jul-2024|131|2|166', 'Aug-2024|131|2|166', 'Sep-2024|131|2|166',
  'Oct-2024|131|2|166', 'Nov-2024|131|2|166', 'Dec-2024|131|2|166',
  'Jan-2025|131|2|166', 'Feb-2025|131|2|166', 'Apr-2024|132|2|166',
  'Jun-2024|132|2|166', 'May-2024|132|2|166', 'Jul-2024|132|2|166',
  'Aug-2024|132|2|166', 'Sep-2024|132|2|166', 'Oct-2024|132|2|166',
  'Nov-2024|132|2|166',
  ... 36 more items
]
Target frequency: 12
Checking entity combinations for indicator 162 with dependencies: [165, 166, 167]
Dependencies needed: [165, 166, 167]
  ✓ Entity Anekal (166|2) has 2/3 dependencies: [165, 166]
  ✓ Entity Test Track  - India (167|2) has 1/3 dependencies: [165]
  ✓ Entity Hosur (130|2) has 3/3 dependencies: [165, 166, 167]
  ✓ Entity Mysore (131|2) has 3/3 dependencies: [165, 166, 167]
  ✓ Entity Nalagarh (132|2) has 2/3 dependencies: [165, 166]
  ✓ Entity Norton Plant (229|3) has 2/3 dependencies: [165, 167]
  ✓ Entity Dummy Testing (278|3) has 1/3 dependencies: [166]
Found 7 entities with available data for indicator 162
Entity-period combinations found: 7
Processing combination: {
  locationId: 166,
  level: 2,
  entity: 'Anekal',
  reporting_period: 'Apr-2024 to Sep-2024',
  availableDependencies: [ 165, 166 ],
  periods: [
    'Apr-2024', 'Aug-2024',
    'Dec-2024', 'Feb-2025',
    'Jan-2025', 'Jul-2024',
    'Jun-2024', 'Mar-2025',
    'May-2024', 'Nov-2024',
    'Oct-2024', 'Sep-2024'
  ]
}

=== Computing derived value for indicator 162 ===
Location: 166, Level: 2, Period: Apr-2024 to Sep-2024
Formula: MT165+MT166+MT167
Dependencies to process: [165, 166, 167]
    Aggregating dependency 165 for entity Anekal (166|2)
    Target reporting period: Apr-2024 to Sep-2024
🔍 Checking if indicator 165 was processed as standalone...
    Checking period range matching for target: Apr-2024 to Sep-2024
    Found 6 periods within target range:
      Apr-2024: 7.452 (frequency: 12, key: Apr-2024|166|2|165)
      May-2024: 10.433 (frequency: 12, key: May-2024|166|2|165)
      Jun-2024: 8.317 (frequency: 12, key: Jun-2024|166|2|165)
      Jul-2024: 8.663 (frequency: 12, key: Jul-2024|166|2|165)
      Aug-2024: 8.424 (frequency: 12, key: Aug-2024|166|2|165)
      Sep-2024: 7.852 (frequency: 12, key: Sep-2024|166|2|165)
      Total aggregated value for 165: 51.14099999999999
  ✓ Aggregated dependency 165: 51.14099999999999
    Aggregating dependency 166 for entity Anekal (166|2)
    Target reporting period: Apr-2024 to Sep-2024
    Checking period range matching for target: Apr-2024 to Sep-2024
    Found 6 periods within target range:
      Apr-2024: 8.8 (frequency: 12, key: Apr-2024|166|2|166)
      May-2024: 5.28 (frequency: 12, key: May-2024|166|2|166)
      Jun-2024: 2.708 (frequency: 12, key: Jun-2024|166|2|166)
      Jul-2024: 5.28 (frequency: 12, key: Jul-2024|166|2|166)
      Aug-2024: 0 (frequency: 12, key: Aug-2024|166|2|166)
      Sep-2024: 2.708 (frequency: 12, key: Sep-2024|166|2|166)
      Total aggregated value for 166: 24.776000000000003
  ✓ Aggregated dependency 166: 24.776000000000003
    Aggregating dependency 167 for entity Anekal (166|2)
    Target reporting period: Apr-2024 to Sep-2024
      No data found for dependency 167 - treating as 0 (entity doesn't have DCF assignment for this indicator)
  ○ Dependency 167: 0 (no DCF assignment for entity Anekal)
Evaluating formula: MT165+MT166+MT167
Available dependency values: { '165': 51.14099999999999, '166': 24.776000000000003, '167': 0 }
  Replaced MT165 with 51.14099999999999
  Replaced MT166 with 24.776000000000003
  Replaced MT167 with 0
Expression after substitution: 51.14099999999999+24.776000000000003+0
Final computed result: 75.917
✅ Computed value for indicator 162: 75.917
   Formula: MT165+MT166+MT167
   Dependencies: {"165":51.14099999999999,"166":24.776000000000003,"167":0}
Derived result computed: 75.917
Processing combination: {
  locationId: 167,
  level: 2,
  entity: 'Test Track  - India',
  reporting_period: 'Apr-2024 to Sep-2024',
  availableDependencies: [ 165 ],
  periods: [
    'Apr-2024', 'Aug-2024',
    'Dec-2024', 'Feb-2025',
    'Jan-2025', 'Jul-2024',
    'Jun-2024', 'Mar-2025',
    'May-2024', 'Nov-2024',
    'Oct-2024', 'Sep-2024'
  ]
}

=== Computing derived value for indicator 162 ===
Location: 167, Level: 2, Period: Apr-2024 to Sep-2024
Formula: MT165+MT166+MT167
Dependencies to process: [165, 166, 167]
    Aggregating dependency 165 for entity Test Track  - India (167|2)
    Target reporting period: Apr-2024 to Sep-2024
🔍 Checking if indicator 165 was processed as standalone...
    Checking period range matching for target: Apr-2024 to Sep-2024
    Found 6 periods within target range:
      Apr-2024: 0.346 (frequency: 12, key: Apr-2024|167|2|165)
      May-2024: 1.549 (frequency: 12, key: May-2024|167|2|165)
      Jun-2024: 1.291 (frequency: 12, key: Jun-2024|167|2|165)
      Jul-2024: 0.532 (frequency: 12, key: Jul-2024|167|2|165)
      Aug-2024: 1.017 (frequency: 12, key: Aug-2024|167|2|165)
      Sep-2024: 0.865 (frequency: 12, key: Sep-2024|167|2|165)
      Total aggregated value for 165: 5.6
  ✓ Aggregated dependency 165: 5.6
    Aggregating dependency 166 for entity Test Track  - India (167|2)
    Target reporting period: Apr-2024 to Sep-2024
      No data found for dependency 166 - treating as 0 (entity doesn't have DCF assignment for this indicator)
  ○ Dependency 166: 0 (no DCF assignment for entity Test Track  - India)
    Aggregating dependency 167 for entity Test Track  - India (167|2)
    Target reporting period: Apr-2024 to Sep-2024
      No data found for dependency 167 - treating as 0 (entity doesn't have DCF assignment for this indicator)
  ○ Dependency 167: 0 (no DCF assignment for entity Test Track  - India)
Evaluating formula: MT165+MT166+MT167
Available dependency values: { '165': 5.6, '166': 0, '167': 0 }
  Replaced MT165 with 5.6
  Replaced MT166 with 0
  Replaced MT167 with 0
Expression after substitution: 5.6+0+0
Final computed result: 5.6
✅ Computed value for indicator 162: 5.6
   Formula: MT165+MT166+MT167
   Dependencies: {"165":5.6,"166":0,"167":0}
Derived result computed: 5.600
Processing combination: {
  locationId: 130,
  level: 2,
  entity: 'Hosur',
  reporting_period: 'Apr-2024 to Sep-2024',
  availableDependencies: [ 165, 166, 167 ],
  periods: [
    'Apr-2024',
    'Apr-2024 to Jun-2024',
    'Aug-2024',
    'Dec-2024',
    'Feb-2025',
    'Jan-2025',
    'Jul-2024',
    'Jul-2024 to Sep-2024',
    'Jun-2024',
    'Mar-2025',
    'May-2024',
    'Nov-2024',
    'Oct-2024',
    'Oct-2024 to Dec-2024',
    'Sep-2024'
  ]
}

=== Computing derived value for indicator 162 ===
Location: 130, Level: 2, Period: Apr-2024 to Sep-2024
Formula: MT165+MT166+MT167
Dependencies to process: [165, 166, 167]
    Aggregating dependency 165 for entity Hosur (130|2)
    Target reporting period: Apr-2024 to Sep-2024
🔍 Checking if indicator 165 was processed as standalone...
🔍 Found 165 data for Hosur: Apr-2024 = 0.000
🔍 Found 165 data for Hosur: May-2024 = 1.916
🔍 Found 165 data for Hosur: Jun-2024 = 9.103
🔍 Found 165 data for Hosur: Jul-2024 = 2.662
🔍 Found 165 data for Hosur: Aug-2024 = 0.000
🔍 Found 165 data for Hosur: Sep-2024 = 6.707
🔍 Found 165 data for Hosur: Oct-2024 = 26.295
🔍 Found 165 data for Hosur: Nov-2024 = 3.362
🔍 Found 165 data for Hosur: Dec-2024 = 10.886
🔍 Found 165 data for Hosur: Jan-2025 = 241.067
🔍 Found 165 data for Hosur: Feb-2025 = 252.828
🔍 Found 165 data for Hosur: Mar-2025 = 225.969
    Checking period range matching for target: Apr-2024 to Sep-2024
    Found 6 periods within target range:
      Apr-2024: 0 (frequency: 12, key: Apr-2024|130|2|165)
      May-2024: 1.916 (frequency: 12, key: May-2024|130|2|165)
      Jun-2024: 9.103 (frequency: 12, key: Jun-2024|130|2|165)
      Jul-2024: 2.662 (frequency: 12, key: Jul-2024|130|2|165)
      Aug-2024: 0 (frequency: 12, key: Aug-2024|130|2|165)
      Sep-2024: 6.707 (frequency: 12, key: Sep-2024|130|2|165)
      Total aggregated value for 165: 20.388
  ✓ Aggregated dependency 165: 20.388
    Aggregating dependency 166 for entity Hosur (130|2)
    Target reporting period: Apr-2024 to Sep-2024
    Checking period range matching for target: Apr-2024 to Sep-2024
    Found 6 periods within target range:
      Apr-2024: 64.96 (frequency: 12, key: Apr-2024|130|2|166)
      May-2024: 69.264 (frequency: 12, key: May-2024|130|2|166)
      Aug-2024: 40.6 (frequency: 12, key: Aug-2024|130|2|166)
      Sep-2024: 88.504 (frequency: 12, key: Sep-2024|130|2|166)
      Jun-2024: 0 (frequency: 12, key: Jun-2024|130|2|166)
      Jul-2024: 0 (frequency: 12, key: Jul-2024|130|2|166)
      Total aggregated value for 166: 263.328
  ✓ Aggregated dependency 166: 263.328
    Aggregating dependency 167 for entity Hosur (130|2)
    Target reporting period: Apr-2024 to Sep-2024
    Checking period range matching for target: Apr-2024 to Sep-2024
    Found 2 periods within target range:
      Apr-2024 to Jun-2024: 0.118 (frequency: 12, key: Apr-2024 to Jun-2024|130|2|167)
      Jul-2024 to Sep-2024: 0.078 (frequency: 12, key: Jul-2024 to Sep-2024|130|2|167)
      Total aggregated value for 167: 0.196
  ✓ Aggregated dependency 167: 0.196
Evaluating formula: MT165+MT166+MT167
Available dependency values: { '165': 20.388, '166': 263.328, '167': 0.196 }
  Replaced MT165 with 20.388
  Replaced MT166 with 263.328
  Replaced MT167 with 0.196
Expression after substitution: 20.388+263.328+0.196
Final computed result: 283.912
✅ Computed value for indicator 162: 283.912
   Formula: MT165+MT166+MT167
   Dependencies: {"165":20.388,"166":263.328,"167":0.196}
Derived result computed: 283.912
Processing combination: {
  locationId: 131,
  level: 2,
  entity: 'Mysore',
  reporting_period: 'Apr-2024 to Sep-2024',
  availableDependencies: [ 165, 166, 167 ],
  periods: [
    'Apr-2024',
    'Apr-2024 to Jun-2024',
    'Aug-2024',
    'Dec-2024',
    'Feb-2025',
    'Jan-2025',
    'Jul-2024',
    'Jul-2024 to Sep-2024',
    'Jun-2024',
    'Mar-2025',
    'May-2024',
    'Nov-2024',
    'Oct-2024',
    'Oct-2024 to Dec-2024',
    'Sep-2024'
  ]
}

=== Computing derived value for indicator 162 ===
Location: 131, Level: 2, Period: Apr-2024 to Sep-2024
Formula: MT165+MT166+MT167
Dependencies to process: [165, 166, 167]
    Aggregating dependency 165 for entity Mysore (131|2)
    Target reporting period: Apr-2024 to Sep-2024
🔍 Checking if indicator 165 was processed as standalone...
    Checking period range matching for target: Apr-2024 to Sep-2024
    Found 6 periods within target range:
      Apr-2024: 60.98 (frequency: 12, key: Apr-2024|131|2|165)
      May-2024: 25.897 (frequency: 12, key: May-2024|131|2|165)
      Jun-2024: 144.609 (frequency: 12, key: Jun-2024|131|2|165)
      Jul-2024: 33.021 (frequency: 12, key: Jul-2024|131|2|165)
      Aug-2024: 12.7 (frequency: 12, key: Aug-2024|131|2|165)
      Sep-2024: 26.034 (frequency: 12, key: Sep-2024|131|2|165)
      Total aggregated value for 165: 303.241
  ✓ Aggregated dependency 165: 303.241
    Aggregating dependency 166 for entity Mysore (131|2)
    Target reporting period: Apr-2024 to Sep-2024
    Checking period range matching for target: Apr-2024 to Sep-2024
    Found 6 periods within target range:
      Apr-2024: 0 (frequency: 12, key: Apr-2024|131|2|166)
      May-2024: 19.5 (frequency: 12, key: May-2024|131|2|166)
      Jun-2024: 0 (frequency: 12, key: Jun-2024|131|2|166)
      Jul-2024: 7.8 (frequency: 12, key: Jul-2024|131|2|166)
      Aug-2024: 0 (frequency: 12, key: Aug-2024|131|2|166)
      Sep-2024: 3.52 (frequency: 12, key: Sep-2024|131|2|166)
      Total aggregated value for 166: 30.82
  ✓ Aggregated dependency 166: 30.82
    Aggregating dependency 167 for entity Mysore (131|2)
    Target reporting period: Apr-2024 to Sep-2024
    Checking period range matching for target: Apr-2024 to Sep-2024
    Found 2 periods within target range:
      Apr-2024 to Jun-2024: 0 (frequency: 12, key: Apr-2024 to Jun-2024|131|2|167)
      Jul-2024 to Sep-2024: 0 (frequency: 12, key: Jul-2024 to Sep-2024|131|2|167)
      Total aggregated value for 167: 0
  ○ Dependency 167: 0 (no DCF assignment for entity Mysore)
Evaluating formula: MT165+MT166+MT167
Available dependency values: { '165': 303.241, '166': 30.82, '167': 0 }
  Replaced MT165 with 303.241
  Replaced MT166 with 30.82
  Replaced MT167 with 0
Expression after substitution: 303.241+30.82+0
Final computed result: 334.061
✅ Computed value for indicator 162: 334.061
   Formula: MT165+MT166+MT167
   Dependencies: {"165":303.241,"166":30.82,"167":0}
Derived result computed: 334.061
Processing combination: {
  locationId: 132,
  level: 2,
  entity: 'Nalagarh',
  reporting_period: 'Apr-2024 to Sep-2024',
  availableDependencies: [ 165, 166 ],
  periods: [
    'Apr-2024', 'Aug-2024',
    'Dec-2024', 'Feb-2025',
    'Jan-2025', 'Jul-2024',
    'Jun-2024', 'Mar-2025',
    'May-2024', 'Nov-2024',
    'Oct-2024', 'Sep-2024'
  ]
}

=== Computing derived value for indicator 162 ===
Location: 132, Level: 2, Period: Apr-2024 to Sep-2024
Formula: MT165+MT166+MT167
Dependencies to process: [165, 166, 167]
    Aggregating dependency 165 for entity Nalagarh (132|2)
    Target reporting period: Apr-2024 to Sep-2024
🔍 Checking if indicator 165 was processed as standalone...
    Checking period range matching for target: Apr-2024 to Sep-2024
    Found 6 periods within target range:
      Apr-2024: 120.789 (frequency: 12, key: Apr-2024|132|2|165)
      May-2024: 120.869 (frequency: 12, key: May-2024|132|2|165)
      Jun-2024: 120.951 (frequency: 12, key: Jun-2024|132|2|165)
      Jul-2024: 121.031 (frequency: 12, key: Jul-2024|132|2|165)
      Aug-2024: 121.114 (frequency: 12, key: Aug-2024|132|2|165)
      Sep-2024: 121.196 (frequency: 12, key: Sep-2024|132|2|165)
      Total aggregated value for 165: 725.95
  ✓ Aggregated dependency 165: 725.95
    Aggregating dependency 166 for entity Nalagarh (132|2)
    Target reporting period: Apr-2024 to Sep-2024
    Checking period range matching for target: Apr-2024 to Sep-2024
    Found 6 periods within target range:
      Apr-2024: 3.114 (frequency: 12, key: Apr-2024|132|2|166)
      Jun-2024: 1.76 (frequency: 12, key: Jun-2024|132|2|166)
      May-2024: 0 (frequency: 12, key: May-2024|132|2|166)
      Jul-2024: 0 (frequency: 12, key: Jul-2024|132|2|166)
      Aug-2024: 0 (frequency: 12, key: Aug-2024|132|2|166)
      Sep-2024: 0 (frequency: 12, key: Sep-2024|132|2|166)
      Total aggregated value for 166: 4.874
  ✓ Aggregated dependency 166: 4.874
    Aggregating dependency 167 for entity Nalagarh (132|2)
    Target reporting period: Apr-2024 to Sep-2024
      No data found for dependency 167 - treating as 0 (entity doesn't have DCF assignment for this indicator)
  ○ Dependency 167: 0 (no DCF assignment for entity Nalagarh)
Evaluating formula: MT165+MT166+MT167
Available dependency values: { '165': 725.95, '166': 4.874, '167': 0 }
  Replaced MT165 with 725.95
  Replaced MT166 with 4.874
  Replaced MT167 with 0
Expression after substitution: 725.95+4.874+0
Final computed result: 730.8240000000001
✅ Computed value for indicator 162: 730.8240000000001
   Formula: MT165+MT166+MT167
   Dependencies: {"165":725.95,"166":4.874,"167":0}
Derived result computed: 730.824
Processing combination: {
  locationId: 229,
  level: 3,
  entity: 'Norton Plant',
  reporting_period: 'Apr-2024 to Sep-2024',
  availableDependencies: [ 165, 167 ],
  periods: [
    'Apr-2024', 'Aug-2024',
    'Dec-2024', 'Feb-2025',
    'Jan-2025', 'Jul-2024',
    'Jun-2024', 'Mar-2025',
    'May-2024', 'Nov-2024',
    'Oct-2024', 'Sep-2024'
  ]
}

=== Computing derived value for indicator 162 ===
Location: 229, Level: 3, Period: Apr-2024 to Sep-2024
Formula: MT165+MT166+MT167
Dependencies to process: [165, 166, 167]
    Aggregating dependency 165 for entity Norton Plant (229|3)
    Target reporting period: Apr-2024 to Sep-2024
🔍 Checking if indicator 165 was processed as standalone...
    Checking period range matching for target: Apr-2024 to Sep-2024
    Found 6 periods within target range:
      Apr-2024: 0.706 (frequency: 12, key: Apr-2024|229|3|165)
      May-2024: 0.706 (frequency: 12, key: May-2024|229|3|165)
      Jun-2024: 0.706 (frequency: 12, key: Jun-2024|229|3|165)
      Jul-2024: 0.706 (frequency: 12, key: Jul-2024|229|3|165)
      Aug-2024: 0.706 (frequency: 12, key: Aug-2024|229|3|165)
      Sep-2024: 0.706 (frequency: 12, key: Sep-2024|229|3|165)
      Total aggregated value for 165: 4.236
  ✓ Aggregated dependency 165: 4.236
    Aggregating dependency 166 for entity Norton Plant (229|3)
    Target reporting period: Apr-2024 to Sep-2024
      No data found for dependency 166 - treating as 0 (entity doesn't have DCF assignment for this indicator)
  ○ Dependency 166: 0 (no DCF assignment for entity Norton Plant)
    Aggregating dependency 167 for entity Norton Plant (229|3)
    Target reporting period: Apr-2024 to Sep-2024
    Checking period range matching for target: Apr-2024 to Sep-2024
    Found 4 periods within target range:
      Jun-2024: 0.15 (frequency: 12, key: Jun-2024|229|3|167)
      Jul-2024: 0.088 (frequency: 12, key: Jul-2024|229|3|167)
      Aug-2024: 0.087 (frequency: 12, key: Aug-2024|229|3|167)
      Sep-2024: 0.076 (frequency: 12, key: Sep-2024|229|3|167)
      Total aggregated value for 167: 0.40099999999999997
  ✓ Aggregated dependency 167: 0.40099999999999997
Evaluating formula: MT165+MT166+MT167
Available dependency values: { '165': 4.236, '166': 0, '167': 0.40099999999999997 }
  Replaced MT165 with 4.236
  Replaced MT166 with 0
  Replaced MT167 with 0.40099999999999997
Expression after substitution: 4.236+0+0.40099999999999997
Final computed result: 4.637
✅ Computed value for indicator 162: 4.637
   Formula: MT165+MT166+MT167
   Dependencies: {"165":4.236,"166":0,"167":0.40099999999999997}
Derived result computed: 4.637
Processing combination: {
  locationId: 278,
  level: 3,
  entity: 'Dummy Testing',
  reporting_period: 'Apr-2024 to Mar-2025',
  availableDependencies: [ 166 ],
  periods: [ 'Apr-2024 to Mar-2025' ]
}

=== Computing derived value for indicator 162 ===
Location: 278, Level: 3, Period: Apr-2024 to Mar-2025
Formula: MT165+MT166+MT167
Dependencies to process: [165, 166, 167]
    Aggregating dependency 165 for entity Dummy Testing (278|3)
    Target reporting period: Apr-2024 to Mar-2025
🔍 Checking if indicator 165 was processed as standalone...
      No data found for dependency 165 - treating as 0 (entity doesn't have DCF assignment for this indicator)
  ○ Dependency 165: 0 (no DCF assignment for entity Dummy Testing)
    Aggregating dependency 166 for entity Dummy Testing (278|3)
    Target reporting period: Apr-2024 to Mar-2025
    Checking period range matching for target: Apr-2024 to Mar-2025
    Found 1 periods within target range:
      Apr-2024 to Mar-2025: 0.1 (frequency: 12, key: Apr-2024 to Mar-2025|278|3|166)
      Total aggregated value for 166: 0.1
  ✓ Aggregated dependency 166: 0.1
    Aggregating dependency 167 for entity Dummy Testing (278|3)
    Target reporting period: Apr-2024 to Mar-2025
      No data found for dependency 167 - treating as 0 (entity doesn't have DCF assignment for this indicator)
  ○ Dependency 167: 0 (no DCF assignment for entity Dummy Testing)
Evaluating formula: MT165+MT166+MT167
Available dependency values: { '165': 0, '166': 0.1, '167': 0 }
  Replaced MT165 with 0
  Replaced MT166 with 0.1
  Replaced MT167 with 0
Expression after substitution: 0+0.1+0
Final computed result: 0.1
✅ Computed value for indicator 162: 0.1
   Formula: MT165+MT166+MT167
   Dependencies: {"165":0,"166":0.1,"167":0}
Derived result computed: 0.100
info: [object Object] {"timestamp":"2025-07-27T12:56:43.363Z"}
