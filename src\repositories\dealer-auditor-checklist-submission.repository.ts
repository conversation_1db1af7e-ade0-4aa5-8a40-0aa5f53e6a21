import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, repository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {DealerAuditorChecklistSubmission, DealerAuditorChecklistSubmissionRelations, VendorCode, UserProfile} from '../models';
import {VendorCodeRepository} from './vendor-code.repository';
import {UserProfileRepository} from './user-profile.repository';

export class DealerAuditorChecklistSubmissionRepository extends DefaultCrudRepository<
  DealerAuditorChecklistSubmission,
  typeof DealerAuditorChecklistSubmission.prototype.id,
  DealerAuditorChecklistSubmissionRelations
> {

  public readonly vendor: BelongsToAccessor<VendorCode, typeof DealerAuditorChecklistSubmission.prototype.id>;

  public readonly dealer: BelongsToAccessor<UserProfile, typeof DealerAuditorChecklistSubmission.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('VendorCodeRepository') protected vendorCodeRepositoryGetter: Getter<VendorCodeRepository>, @repository.getter('UserProfileRepository') protected userProfileRepositoryGetter: Getter<UserProfileRepository>,
  ) {
    super(DealerAuditorChecklistSubmission, dataSource);
    this.dealer = this.createBelongsToAccessorFor('dealer', userProfileRepositoryGetter,);
    this.registerInclusionResolver('dealer', this.dealer.inclusionResolver);

    this.vendor = this.createBelongsToAccessorFor('vendor', vendorCodeRepositoryGetter,);
    this.registerInclusionResolver('vendor', this.vendor.inclusionResolver);
  }
}
