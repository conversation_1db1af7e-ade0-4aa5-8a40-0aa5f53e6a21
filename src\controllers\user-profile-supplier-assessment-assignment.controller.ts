import {authenticate} from '@loopback/authentication';
import {
  UserCredentialsRepository,
  UserRepository
} from '@loopback/authentication-jwt';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  HttpErrors,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {UserProfile as LibUserProfile, SecurityBindings, securityId} from '@loopback/security';
import {DateTime} from 'luxon';
import {v4 as uuidv4} from 'uuid';
import {
  SupplierAssessmentAssignment,
  UserProfile,
} from '../models';
import {SupplierAssessmentAssignmentRepository, UserProfileRepository, VendorCodeRepository} from '../repositories';
import {SqsService} from '../services/sqs-service.service';
import {UserProfileController} from './user-profile.controller';
@authenticate('cognito-tvs', 'jwt')
export class UserProfileSupplierAssessmentAssignmentController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
    @repository(UserCredentialsRepository) protected userCredentialsRepository: UserCredentialsRepository,
    @repository(UserRepository) protected userRepository: UserRepository,
    @inject('controllers.UserProfileController') public userProfileController: UserProfileController,
    @repository(VendorCodeRepository) protected vendorCodeRepository: VendorCodeRepository,
    @repository(SupplierAssessmentAssignmentRepository) protected supplierAssessmentAssignmentRepository: SupplierAssessmentAssignmentRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) { }

  @get('/user-profiles/{id}/supplier-assessment-assignments', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many SupplierAssessmentAssignment',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SupplierAssessmentAssignment)},
          },
        },
      },
    },
  })
  async find(
    @inject(SecurityBindings.USER)
    currentUserProfile: LibUserProfile,
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<SupplierAssessmentAssignment>,
  ): Promise<SupplierAssessmentAssignment[]> {

    const userId = currentUserProfile[securityId];
    const userDetail = await this.userRepository.findById(userId);
    const userProfileDetail = await this.userProfileRepository.findOne({
      where: {userId: userId}, include: [
        {
          relation: 'vendorCodes'
        },
      ], limit: 1
    });


    const supplierId = (filter?.where as {supplierId?: number})?.supplierId;
    const vendorCode = (filter?.where as {vendorCode?: number})?.vendorCode;
    if (!userProfileDetail || userProfileDetail.id !== supplierId) {
      throw new HttpErrors.Forbidden('You are not authorized to access this resource');
    }

    const match = userProfileDetail?.vendorCodes?.some(vc =>
      vc.code === vendorCode && vc.clientId === id
    );

    if (!match) {
      throw new HttpErrors.Forbidden('You are not authorized to access this resource');
    }
    return this.userProfileRepository.supplierAssessmentAssignments(id).find(filter);
  }

  @get('/user-profiles/{id}/supplier-assessment-assignments-global', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many SupplierAssessmentAssignment',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SupplierAssessmentAssignment)},
          },
        },
      },
    },
  })
  async findGlobal(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<SupplierAssessmentAssignment>,
  ): Promise<SupplierAssessmentAssignment[]> {

    return this.userProfileRepository.supplierAssessmentAssignments(id).find(filter);
  }
  // @post('/user-profiles/{id}/supplier-assessment-assignmentss', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile model instance',
  //       content: {'application/json': {schema: getModelSchemaRef(SupplierAssessmentAssignment)}},
  //     },
  //   },
  // })
  // async create(
  //   @param.path.number('id') id: typeof UserProfile.prototype.id,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(SupplierAssessmentAssignment, {
  //           title: 'NewSupplierAssessmentAssignmentInUserProfile',
  //           exclude: ['id'],
  //           optional: ['userProfileId']
  //         }),
  //       },
  //     },
  //   }) supplierAssessmentAssignment: Omit<SupplierAssessmentAssignment, 'id'>,
  // ): Promise<SupplierAssessmentAssignment> {
  //   return this.userProfileRepository.supplierAssessmentAssignments(id).create({id: uuidv4(), ...supplierAssessmentAssignment});
  // }
  @post('/user-profiles/{id}/supplier-assessment-assignments-global', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(SupplierAssessmentAssignment)}},
      },
    },
  })
  async createCustom(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAssessmentAssignment, {
            title: 'NewSupplierAssessmentAssignmentInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId'],
          }),
        },
      },
    }) supplierAssessmentAssignment: Omit<SupplierAssessmentAssignment, 'id'>,
  ): Promise<SupplierAssessmentAssignment> {
    let createdAssignment;
    const maxAttempts = 10;

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      const uuid = uuidv4()
      try {
        createdAssignment = await this.userProfileRepository.supplierAssessmentAssignments(id).create({
          id: uuid,
          ...supplierAssessmentAssignment
        });

        break; // success, break the loop
      } catch (err) {
        // handle only duplicate key error (adjust code for your DB if needed)
        const isDuplicateError =
          err.code === 'ER_DUP_ENTRY' ||       // MySQL
          err.code === 'SQLITE_CONSTRAINT' ||  // SQLite
          err.statusCode === 409;              // LoopBack's default for conflict

        if (!isDuplicateError) throw err; // rethrow other errors

        // else log and continue
        console.warn(`Duplicate ID on attempt ${attempt + 1}, retrying...`);
      }
    }

    // final failure after all attempts
    if (!createdAssignment) {
      throw new HttpErrors.InternalServerError('Could not generate unique assignment ID after multiple attempts.');
    } else {
      console.log('createdAssignment', createdAssignment);
    }



    const assessmentEndDate = DateTime.fromISO(supplierAssessmentAssignment.assessmentEndDate ?? '', {zone: 'utc'}).plus({'day': 1});
    // 2. Fetch vendor data based on vendorId in assignment
    const vendorData = await this.vendorCodeRepository.findById(supplierAssessmentAssignment.vendorId);
    const vendorSpoc = await this.userProfileController.filteredUP({where: {id: vendorData.userProfileId}});
    // const vendorSpoc = vendorSpocArray?.[0];
    const supplierOtherSpoc = this.getUniqueValidEmails([vendorData]).flatMap((x: any) => x.emails).filter((x: any) => x)
    const adminObj = await this.userProfileRepository.findById(id);

    // 4. Send email
    if (vendorData && vendorSpoc?.[0]?.email) {
      const subject = `Submit MSI Self-Assessment – ${vendorData.supplierName}`;
      const body = `
        <p>Dear ${vendorData.supplierName},</p>
        <p>Hope you're doing well.</p>

        <p>
          As part of our <strong>"My Sustainability Index"</strong> program, which aims to enhance sustainable practices
          across our value chain, we kindly request you to complete the <strong>"Self-Assessment"</strong> form.
        </p>

        <p>
          Your Self-Assessment (${createdAssignment.id}) is now live on the Navigos Sustainability Platform. We request you to complete the
          assessment by <strong>${assessmentEndDate.toFormat('dd/MM/yyyy')}</strong>.
        </p>

        <p><strong>What You Need to Do:</strong></p>
        <ul>
          <li><strong>Log in</strong> to the Supplier Portal: <a href="${adminObj?.supplierPortalUrl}">${adminObj?.supplierPortalUrl}</a></li>
          <li>Navigate to the Self-Assessment section in the "My Actions" tab and submit your responses</li>
          <li><strong>Complete</strong> all sections with accurate and updated information</li>
        </ul>

        <p>
          Upload supporting evidence where appropriate (certifications, licenses, reports, etc.).
        </p>

        <p>
          For support, contact <a href="mailto:<EMAIL>"><EMAIL></a> and copy
          <a href="mailto:<EMAIL>"><EMAIL></a>.
        </p>

        <p>
          Your participation is vital to the success of the <strong>"My Sustainability Index"</strong> program. Thank you for
          your commitment to building a sustainable future.
        </p>

        <p>Warm regards,<br/><strong>TVS Motor Company Limited</strong></p>
        <p style='font-style:italic'><em>This is an automated message. Please do not respond to this mail</em></p>
      `;

      try {
        const info = this.sqsService.sendEmail([vendorSpoc[0]?.email, ...supplierOtherSpoc], subject, body, ['<EMAIL>', '<EMAIL>'])

      } catch (error) {
        console.error('Error sending email:', error);
        throw new Error('Failed to send email');
      }
    }

    return createdAssignment;
  }


  @patch('/user-profiles/{id}/supplier-assessment-assignments', {
    responses: {
      '200': {
        description: 'UserProfile.SupplierAssessmentAssignment PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAssessmentAssignment, {partial: true}),
        },
      },
    })
    supplierAssessmentAssignment: Partial<SupplierAssessmentAssignment>,
    @param.query.object('where', getWhereSchemaFor(SupplierAssessmentAssignment)) where?: Where<SupplierAssessmentAssignment>,
  ): Promise<Count> {
    return this.userProfileRepository.supplierAssessmentAssignments(id).patch(supplierAssessmentAssignment, where);
  }

  @del('/user-profiles/{id}/supplier-assessment-assignments', {
    responses: {
      '200': {
        description: 'UserProfile.SupplierAssessmentAssignment DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(SupplierAssessmentAssignment)) where?: Where<SupplierAssessmentAssignment>,
  ): Promise<Count> {
    return this.userProfileRepository.supplierAssessmentAssignments(id).delete(where);
  }

  @post('/supplier-assessment-assignments/{id}/resend-assignment-email', {
    responses: {
      '200': {
        description: 'Email resent successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                status: {type: 'boolean'},
                message: {type: 'string'}
              }
            }
          }
        }
      },
    },
  })
  async resendSubmissionEmail(
    @param.path.string('id') supplierAssessmentAssignmentId: string,
  ): Promise<{status: boolean; message: string}> {
    try {
      // 1. Find the supplier assessment assignment
      const assignment = await this.supplierAssessmentAssignmentRepository.findById(supplierAssessmentAssignmentId);

      if (!assignment) {
        throw new HttpErrors.NotFound('Supplier assessment assignment not found');
      }

      // 2. Get the admin/user profile who created the assignment
      const adminObj = await this.userProfileRepository.findById(assignment.userProfileId);

      if (!adminObj) {
        throw new HttpErrors.NotFound('Admin user profile not found');
      }

      // 3. Fetch vendor data based on vendorId in assignment
      const vendorData = await this.vendorCodeRepository.findById(assignment.vendorId);

      if (!vendorData) {
        throw new HttpErrors.NotFound('Vendor data not found');
      }

      // 4. Get vendor SPOC and other emails
      const vendorSpoc = await this.userProfileController.filteredUP({where: {id: vendorData.userProfileId}});
      const supplierOtherSpoc = this.getUniqueValidEmails([vendorData]).flatMap((x: any) => x.emails).filter((x: any) => x);

      // 5. Calculate assessment end date
      const assessmentEndDate = DateTime.fromISO(assignment.assessmentEndDate ?? '', {zone: 'utc'}).plus({'day': 1});

      // 6. Send email if vendor SPOC email exists
      if (vendorData && vendorSpoc?.[0]?.email) {
        const subject = `Submit MSI Self-Assessment – ${vendorData.supplierName}`;
        const body = `
          <p>Dear ${vendorData.supplierName},</p>
          <p>Hope you're doing well.</p>

          <p>
            As part of our <strong>"My Sustainability Index"</strong> program, which aims to enhance sustainable practices
            across our value chain, we kindly request you to complete the <strong>"Self-Assessment"</strong> form.
          </p>

          <p>
            Your Self-Assessment (${assignment.id}) is now live on the Navigos Sustainability Platform. We request you to complete the
            assessment by <strong>${assessmentEndDate.toFormat('dd/MM/yyyy')}</strong>.
          </p>

          <p><strong>What You Need to Do:</strong></p>
          <ul>
            <li><strong>Log in</strong> to the Supplier Portal: <a href="${adminObj?.supplierPortalUrl}">${adminObj?.supplierPortalUrl}</a></li>
            <li>Navigate to the Self-Assessment section in the "My Actions" tab and submit your responses</li>
            <li><strong>Complete</strong> all sections with accurate and updated information</li>
          </ul>

          <p>
            Upload supporting evidence where appropriate (certifications, licenses, reports, etc.).
          </p>

          <p>
            For support, contact <a href="mailto:<EMAIL>"><EMAIL></a> and copy
            <a href="mailto:<EMAIL>"><EMAIL></a>.
          </p>

          <p>
            Your participation is vital to the success of the <strong>"My Sustainability Index"</strong> program. Thank you for
            your commitment to building a sustainable future.
          </p>

          <p>Warm regards,<br/><strong>TVS Motor Company Limited</strong></p>
          <p style='font-style:italic'><em>This is an automated message. Please do not respond to this mail</em></p>
        `;

        try {
          await this.sqsService.sendEmail(
            [vendorSpoc[0]?.email, ...supplierOtherSpoc],
            subject,
            body,
            ['<EMAIL>', '<EMAIL>']
          );

          console.log(`Email resent successfully for assignment: ${supplierAssessmentAssignmentId}`);

          return {
            status: true,
            message: 'Email resent successfully'
          };

        } catch (error) {
          console.error('Error sending email:', error);
          throw new HttpErrors.InternalServerError('Failed to send email');
        }
      } else {
        throw new HttpErrors.BadRequest('Vendor SPOC email not found');
      }

    } catch (error) {
      console.error('Error in resendSubmissionEmail:', error);

      if (error instanceof HttpErrors.HttpError) {
        throw error;
      }

      return {
        status: false,
        message: `Failed to resend email: ${error.message}`
      };
    }
  }

  getUniqueValidEmails(data: any) {
    const seenEmails = new Set(); // Global tracker for unique emails

    return data.map(({code, supplierEmail3, supplierEmail2}: any) => {
      const uniqueEmails = new Set(); // Local tracker to prevent duplicate emails within the same code

      [supplierEmail3, supplierEmail2].forEach((email: any) => {
        if (this.isValidEmail(email) && !seenEmails.has(email)) {
          uniqueEmails.add(email);
          seenEmails.add(email); // Track globally
        }
      });

      return {code, emails: [...uniqueEmails]};
    }).filter((entry: any) => entry.emails.length > 0); // Remove empty email lists
  }

  isValidEmail(email: any) {
    if (!email?.trim()) return false; // Returns false for null, undefined, or empty string
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };
}
