import {belongsTo, Entity, model, property} from '@loopback/repository';
import {UserProfile} from './user-profile.model';

@model()
export class Vehicle extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  title: string;

  @property({
    type: 'string',
    required: true,
  })
  created_on: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
    required: true,
  })
  created_by: number;

  @property({
    type: 'number',
  })
  updated_by?: number;

  @belongsTo(() => UserProfile)
  userProfileId: number;

  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<Vehicle>) {
    super(data);
  }
}

export interface VehicleRelations {
  // describe navigational properties here
}

export type VehicleWithRelations = Vehicle & VehicleRelations;
